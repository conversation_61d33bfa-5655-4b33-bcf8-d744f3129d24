<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<jsp:include page="/WEB-INF/views/common/layout.jsp">
    <jsp:param name="title" value="视频流" />
    <jsp:param name="content" value="/WEB-INF/views/camera/stream-content.jsp" />
    <jsp:param name="additionalStyles" value="
        /* 视频流页面样式 */
        .stream-container {
            background-color: #000;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            height: calc(100vh - 200px);
            min-height: 500px;
        }
        .stream-container img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        .stream-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .stream-controls {
            position: absolute;
            bottom: 20px;
            left: 0;
            width: 100%;
            padding: 0 20px;
            display: flex;
            justify-content: center;
            z-index: 10;
        }
        .stream-controls .btn {
            margin: 0 5px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
        }
        .stream-controls .btn:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }
        .stream-controls .btn-primary {
            background-color: rgba(13, 110, 253, 0.7);
        }
        .stream-controls .btn-primary:hover {
            background-color: rgba(13, 110, 253, 0.9);
        }
        .stream-info {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 10;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 10px 15px;
            border-radius: 5px;
        }
        .camera-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .camera-online {
            background-color: #28a745;
        }
        .camera-offline {
            background-color: #dc3545;
        }
    " />
    <jsp:param name="scripts" value="
        <script>
            // 页面加载完成后执行
            document.addEventListener('DOMContentLoaded', function() {
                // 获取视频流URL
                const cameraId = '${camera.id}';

                // 如果摄像头在线，加载视频流
                if (${camera.status} === 1) {
                    loadVideoStream(cameraId);
                }

                // 控制按钮点击事件
                document.querySelectorAll('.control-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const action = this.getAttribute('data-action');
                        controlCamera(cameraId, action);
                    });
                });

                // 连接/断开摄像头按钮点击事件
                const connectBtn = document.getElementById('connectBtn');
                if (connectBtn) {
                    connectBtn.addEventListener('click', function() {
                        const action = this.getAttribute('data-action');

                        fetch('${pageContext.request.contextPath}/camera/control', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'cameraId=' + cameraId + '&action=' + action
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                alert(data.message);
                                // 刷新页面
                                window.location.reload();
                            } else {
                                alert('操作失败: ' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('操作失败，请稍后重试');
                        });
                    });
                }

                // 全屏按钮点击事件
                const fullscreenBtn = document.getElementById('fullscreenBtn');
                if (fullscreenBtn) {
                    fullscreenBtn.addEventListener('click', function() {
                        const streamContainer = document.querySelector('.stream-container');
                        if (streamContainer) {
                            if (streamContainer.requestFullscreen) {
                                streamContainer.requestFullscreen();
                            } else if (streamContainer.webkitRequestFullscreen) { /* Safari */
                                streamContainer.webkitRequestFullscreen();
                            } else if (streamContainer.msRequestFullscreen) { /* IE11 */
                                streamContainer.msRequestFullscreen();
                            }
                        }
                    });
                }
            });

            // 加载视频流
            function loadVideoStream(cameraId) {
                // 获取视频流URL
                fetch('${pageContext.request.contextPath}/camera/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'cameraId=' + cameraId + '&action=getStreamUrl'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('RTSP URL:', data.rtspUrl);

                        // 显示RTSP URL供用户使用
                        const rtspUrlElement = document.getElementById('rtspUrl');
                        const rtspInfoElement = document.getElementById('rtspInfo');

                        if (rtspUrlElement && rtspInfoElement) {
                            rtspUrlElement.textContent = data.rtspUrl;
                            rtspInfoElement.style.display = 'block';
                        }

                        // 尝试加载HLS流（如果有转换服务）
                        tryLoadHLSStream(data.rtspUrl, cameraId);

                    } else {
                        console.error('获取视频流失败:', data.message);
                        showVideoError('获取视频流失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showVideoError('网络错误，无法获取视频流');
                });
            }

            // 尝试加载HLS流
            function tryLoadHLSStream(rtspUrl, cameraId) {
                // 这里可以调用您的RTSP到HLS转换服务
                // 例如：http://localhost:8888/live/camera1.m3u8

                const hlsUrl = convertRtspToHls(rtspUrl, cameraId);
                const video = document.getElementById('videoPlayer');
                const videoSource = document.getElementById('videoSource');

                if (video && videoSource) {
                    if (hlsUrl) {
                        // 如果有HLS URL，尝试播放
                        videoSource.src = hlsUrl;
                        video.load();

                        video.addEventListener('loadstart', function() {
                            console.log('开始加载视频流...');
                        });

                        video.addEventListener('canplay', function() {
                            console.log('视频流可以播放');
                        });

                        video.addEventListener('error', function(e) {
                            console.error('视频播放错误:', e);
                            showVideoError('视频流播放失败，请检查摄像头连接');
                        });
                    } else {
                        // 如果没有HLS转换，显示提示信息
                        showVideoError('浏览器无法直接播放RTSP流，请使用VLC播放器打开下方的RTSP地址');
                    }
                }
            }

            // 转换RTSP URL到HLS URL（需要配置流媒体服务器）
            function convertRtspToHls(rtspUrl, cameraId) {
                // 检查是否有流媒体服务器运行
                // 如果您已经启动了rtsp-to-hls-server.js，取消下面的注释

                if (cameraId === 1 || cameraId === '1') {
                    // 对应rtsp-to-hls-server.js中配置的camera1
                    return 'http://localhost:8888/live/camera1.m3u8';
                }

                // 动态生成HLS URL（如果您的服务器支持多摄像头）
                // return 'http://localhost:8888/live/camera' + cameraId + '.m3u8';

                // 如果没有转换服务，返回null
                return null;
            }

            // 显示视频错误信息
            function showVideoError(message) {
                const videoContainer = document.getElementById('videoContainer');
                if (videoContainer) {
                    videoContainer.innerHTML = '<div class="alert alert-warning text-center py-4">' +
                        '<i class="bi bi-exclamation-triangle fs-1 mb-3 d-block"></i>' +
                        '<h5>视频流加载失败</h5>' +
                        '<p>' + message + '</p>' +
                        '<button class="btn btn-primary" onclick="location.reload()">重新加载</button>' +
                        '</div>';
                }
            }

            // 复制RTSP URL到剪贴板
            function copyRtspUrl() {
                const rtspUrl = document.getElementById('rtspUrl').textContent;
                navigator.clipboard.writeText(rtspUrl).then(function() {
                    alert('RTSP地址已复制到剪贴板');
                }).catch(function(err) {
                    console.error('复制失败:', err);
                    // 备用方案
                    const textArea = document.createElement('textarea');
                    textArea.value = rtspUrl;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    alert('RTSP地址已复制到剪贴板');
                });
            }

            // 控制摄像头
            function controlCamera(cameraId, action) {
                fetch('${pageContext.request.contextPath}/camera/control', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'cameraId=' + cameraId + '&action=' + action
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('控制成功:', data.message);
                    } else {
                        console.error('控制失败:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            }
        </script>
    " />
</jsp:include>
