<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<jsp:include page="/WEB-INF/views/common/layout.jsp">
    <jsp:param name="title" value="摄像头详情" />
    <jsp:param name="content" value="/WEB-INF/views/camera/detail-content.jsp" />
    <jsp:param name="additionalStyles" value="
        /* 摄像头详情卡片样式 */
        .camera-info-card {
            border-radius: 12px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            transition: all 0.3s ease;
        }
        .camera-info-card:hover {
            box-shadow: 0 0 20px rgba(0,0,0,0.15);
            transform: translateY(-3px);
        }
        .camera-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .camera-online {
            background-color: #28a745;
        }
        .camera-offline {
            background-color: #dc3545;
        }
        .camera-stream-container {
            height: 400px;
            background-color: #000;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
        }
        .camera-stream-container img {
            max-width: 100%;
            max-height: 100%;
        }
        .camera-stream-container .camera-icon {
            font-size: 5rem;
            color: #6c757d;
        }
        .camera-stream-container .stream-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .camera-control-panel {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }
        .control-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 5px;
            font-size: 1.2rem;
        }
        .person-record-card {
            border-radius: 8px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        .person-record-card:hover {
            transform: translateY(-3px);
        }
        .person-count-badge {
            font-size: 1rem;
            padding: 5px 10px;
            border-radius: 20px;
        }
    " />
    <jsp:param name="scripts">
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            // 页面加载完成后执行
            document.addEventListener('DOMContentLoaded', function() {
                // 初始化图表
                initPersonCountChart();

                // 控制按钮点击事件
                document.querySelectorAll('.control-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const cameraId = this.getAttribute('data-camera-id');
                        const action = this.getAttribute('data-action');
                        controlCamera(cameraId, action);
                    });
                });

                // 连接/断开摄像头按钮点击事件
                const connectBtn = document.getElementById('connectBtn');
                if (connectBtn) {
                    connectBtn.addEventListener('click', function() {
                        const cameraId = this.getAttribute('data-camera-id');
                        const action = this.getAttribute('data-action');

                        fetch('${pageContext.request.contextPath}/camera/control', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'cameraId=' + cameraId + '&action=' + action
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                alert(data.message);
                                // 刷新页面
                                window.location.reload();
                            } else {
                                alert('操作失败: ' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('操作失败，请稍后重试');
                        });
                    });
                }

                // 查看视频流按钮点击事件
                const viewStreamBtn = document.getElementById('viewStreamBtn');
                if (viewStreamBtn) {
                    viewStreamBtn.addEventListener('click', function() {
                        const cameraId = this.getAttribute('data-camera-id');
                        window.location.href = '${pageContext.request.contextPath}/camera/stream?id=' + cameraId;
                    });
                }
            });

            // 初始化人数统计图表
            function initPersonCountChart() {
                const ctx = document.getElementById('personCountChart');
                if (ctx) {
                    // 从页面中获取数据
                    const chartData = getChartDataFromPage();

                    new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: chartData.labels,
                            datasets: [{
                                label: '人数',
                                data: chartData.data,
                                borderColor: 'rgba(75, 192, 192, 1)',
                                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        precision: 0
                                    }
                                }
                            }
                        }
                    });
                }
            }

            // 从页面中获取图表数据
            function getChartDataFromPage() {
                const labels = [];
                const data = [];

                // 从隐藏的数据元素中获取数据
                const dataElements = document.querySelectorAll('.chart-data-item');
                dataElements.forEach(function(element) {
                    labels.push(element.getAttribute('data-time'));
                    data.push(parseInt(element.getAttribute('data-count')) || 0);
                });

                return { labels: labels, data: data };
            }

            // 控制摄像头
            function controlCamera(cameraId, action) {
                fetch('${pageContext.request.contextPath}/camera/control', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'cameraId=' + cameraId + '&action=' + action
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast(data.message, 'success');
                        // 如果是连接/断开操作，刷新页面以更新状态
                        if (action === 'connect' || action === 'disconnect') {
                            setTimeout(() => {
                                location.reload();
                            }, 1500);
                        }
                    } else {
                        showToast('操作失败: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('操作失败，请稍后重试', 'error');
                });
            }

            // 复制到剪贴板
            function copyToClipboard(text) {
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(text).then(function() {
                        showToast('RTSP地址已复制到剪贴板', 'success');
                    }).catch(function(err) {
                        console.error('复制失败:', err);
                        fallbackCopyTextToClipboard(text);
                    });
                } else {
                    fallbackCopyTextToClipboard(text);
                }
            }

            // 备用复制方法
            function fallbackCopyTextToClipboard(text) {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.top = '0';
                textArea.style.left = '0';
                textArea.style.width = '2em';
                textArea.style.height = '2em';
                textArea.style.padding = '0';
                textArea.style.border = 'none';
                textArea.style.outline = 'none';
                textArea.style.boxShadow = 'none';
                textArea.style.background = 'transparent';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                try {
                    const successful = document.execCommand('copy');
                    if (successful) {
                        showToast('RTSP地址已复制到剪贴板', 'success');
                    } else {
                        showToast('复制失败，请手动复制', 'error');
                    }
                } catch (err) {
                    console.error('复制失败:', err);
                    showToast('复制失败，请手动复制', 'error');
                }

                document.body.removeChild(textArea);
            }

            // 显示Toast提示
            function showToast(message, type = 'success') {
                // 创建Toast容器（如果不存在）
                let toastContainer = document.querySelector('.toast-container');
                if (!toastContainer) {
                    toastContainer = document.createElement('div');
                    toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                    toastContainer.style.zIndex = '9999';
                    document.body.appendChild(toastContainer);
                }

                const toastId = 'toast-' + Date.now();
                let iconClass, title, bgClass;

                switch(type) {
                    case 'success':
                        iconClass = 'bi-check-circle-fill text-success';
                        title = '成功';
                        bgClass = 'bg-light';
                        break;
                    case 'error':
                        iconClass = 'bi-exclamation-triangle-fill text-danger';
                        title = '错误';
                        bgClass = 'bg-light';
                        break;
                    case 'info':
                        iconClass = 'bi-info-circle-fill text-info';
                        title = '信息';
                        bgClass = 'bg-light';
                        break;
                    default:
                        iconClass = 'bi-check-circle-fill text-success';
                        title = '成功';
                        bgClass = 'bg-light';
                }

                const toastHtml = '&lt;div id="' + toastId + '" class="toast ' + bgClass + '" role="alert" aria-live="assertive" aria-atomic="true"&gt;' +
                    '&lt;div class="toast-header"&gt;' +
                        '&lt;i class="bi ' + iconClass + ' me-2"&gt;&lt;/i&gt;' +
                        '&lt;strong class="me-auto"&gt;' + title + '&lt;/strong&gt;' +
                        '&lt;button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"&gt;&lt;/button&gt;' +
                    '&lt;/div&gt;' +
                    '&lt;div class="toast-body"&gt;' +
                        message +
                    '&lt;/div&gt;' +
                '&lt;/div&gt;';

                toastContainer.insertAdjacentHTML('beforeend', toastHtml);

                const toastElement = document.getElementById(toastId);
                const toast = new bootstrap.Toast(toastElement, {
                    autohide: true,
                    delay: 3000
                });

                toast.show();

                // 自动移除Toast元素
                toastElement.addEventListener('hidden.bs.toast', function() {
                    toastElement.remove();
                });
            }
        </script>
    </jsp:param>
</jsp:include>
