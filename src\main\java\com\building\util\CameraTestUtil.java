package com.building.util;

import com.building.model.Camera;
import java.io.IOException;
import java.net.InetAddress;
import java.net.Socket;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 摄像头测试工具类
 * 用于测试摄像头连接、验证配置等
 */
public class CameraTestUtil {
    
    private static final int DEFAULT_TIMEOUT = 5000; // 5秒超时
    private static final Pattern IP_PATTERN = Pattern.compile(
        "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
    );
    
    /**
     * 测试摄像头网络连通性
     * @param camera 摄像头对象
     * @return 测试结果
     */
    public static CameraTestResult testNetworkConnectivity(Camera camera) {
        CameraTestResult result = new CameraTestResult();
        result.setCameraId(camera.getId());
        result.setCameraName(camera.getName());
        
        try {
            // 测试IP地址可达性
            InetAddress address = InetAddress.getByName(camera.getIpAddress());
            boolean reachable = address.isReachable(DEFAULT_TIMEOUT);
            
            if (reachable) {
                result.setNetworkReachable(true);
                result.addMessage("网络连通性测试：成功");
                
                // 测试端口连接
                boolean portOpen = testPortConnection(camera.getIpAddress(), camera.getPort());
                result.setPortOpen(portOpen);
                
                if (portOpen) {
                    result.addMessage("端口连接测试：成功 (端口 " + camera.getPort() + " 可访问)");
                    result.setOverallSuccess(true);
                } else {
                    result.addMessage("端口连接测试：失败 (端口 " + camera.getPort() + " 不可访问)");
                    result.setOverallSuccess(false);
                }
            } else {
                result.setNetworkReachable(false);
                result.setPortOpen(false);
                result.setOverallSuccess(false);
                result.addMessage("网络连通性测试：失败 (IP地址不可达)");
            }
            
        } catch (IOException e) {
            result.setNetworkReachable(false);
            result.setPortOpen(false);
            result.setOverallSuccess(false);
            result.addMessage("网络测试异常：" + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 测试端口连接
     * @param host 主机地址
     * @param port 端口号
     * @return 是否连接成功
     */
    private static boolean testPortConnection(String host, int port) {
        try (Socket socket = new Socket()) {
            socket.connect(new java.net.InetSocketAddress(host, port), DEFAULT_TIMEOUT);
            return true;
        } catch (SocketTimeoutException e) {
            System.err.println("端口连接超时：" + host + ":" + port);
            return false;
        } catch (IOException e) {
            System.err.println("端口连接失败：" + host + ":" + port + " - " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 验证摄像头配置
     * @param camera 摄像头对象
     * @return 验证结果
     */
    public static CameraTestResult validateCameraConfig(Camera camera) {
        CameraTestResult result = new CameraTestResult();
        result.setCameraId(camera.getId());
        result.setCameraName(camera.getName());
        
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        // 验证基本信息
        if (camera.getName() == null || camera.getName().trim().isEmpty()) {
            errors.add("摄像头名称不能为空");
        }
        
        // 验证IP地址
        if (camera.getIpAddress() == null || camera.getIpAddress().trim().isEmpty()) {
            errors.add("IP地址不能为空");
        } else if (!IP_PATTERN.matcher(camera.getIpAddress()).matches()) {
            errors.add("IP地址格式无效：" + camera.getIpAddress());
        }
        
        // 验证端口号
        if (camera.getPort() <= 0 || camera.getPort() > 65535) {
            errors.add("端口号无效：" + camera.getPort() + " (有效范围：1-65535)");
        }
        
        // 验证RTSP URL
        if (camera.getRtspUrl() == null || camera.getRtspUrl().trim().isEmpty()) {
            warnings.add("RTSP URL为空，将自动生成");
        } else if (!camera.getRtspUrl().toLowerCase().startsWith("rtsp://")) {
            errors.add("RTSP URL格式无效，必须以 rtsp:// 开头");
        }
        
        // 验证认证信息
        if (camera.getUsername() == null || camera.getUsername().trim().isEmpty()) {
            warnings.add("用户名为空，可能影响RTSP连接");
        }
        
        if (camera.getPassword() == null || camera.getPassword().trim().isEmpty()) {
            warnings.add("密码为空，可能影响RTSP连接");
        }
        
        // 设置结果
        result.setOverallSuccess(errors.isEmpty());
        
        for (String error : errors) {
            result.addMessage("错误：" + error);
        }
        
        for (String warning : warnings) {
            result.addMessage("警告：" + warning);
        }
        
        if (errors.isEmpty() && warnings.isEmpty()) {
            result.addMessage("配置验证：所有配置项都正确");
        }
        
        return result;
    }
    
    /**
     * 生成摄像头诊断报告
     * @param camera 摄像头对象
     * @return 诊断报告
     */
    public static String generateDiagnosticReport(Camera camera) {
        StringBuilder report = new StringBuilder();
        report.append("=== 摄像头诊断报告 ===\n");
        report.append("摄像头名称：").append(camera.getName()).append("\n");
        report.append("IP地址：").append(camera.getIpAddress()).append("\n");
        report.append("端口：").append(camera.getPort()).append("\n");
        report.append("RTSP URL：").append(camera.getRtspUrl()).append("\n");
        report.append("状态：").append(camera.getStatus() == 1 ? "在线" : "离线").append("\n");
        report.append("品牌：").append(camera.getBrand() != null ? camera.getBrand() : "未知").append("\n");
        report.append("型号：").append(camera.getModel() != null ? camera.getModel() : "未知").append("\n");
        report.append("\n");
        
        // 配置验证
        CameraTestResult configResult = validateCameraConfig(camera);
        report.append("=== 配置验证 ===\n");
        for (String message : configResult.getMessages()) {
            report.append(message).append("\n");
        }
        report.append("\n");
        
        // 网络连通性测试
        CameraTestResult networkResult = testNetworkConnectivity(camera);
        report.append("=== 网络连通性测试 ===\n");
        for (String message : networkResult.getMessages()) {
            report.append(message).append("\n");
        }
        
        return report.toString();
    }
    
    /**
     * 摄像头测试结果类
     */
    public static class CameraTestResult {
        private int cameraId;
        private String cameraName;
        private boolean overallSuccess;
        private boolean networkReachable;
        private boolean portOpen;
        private List<String> messages = new ArrayList<>();
        
        // Getters and Setters
        public int getCameraId() { return cameraId; }
        public void setCameraId(int cameraId) { this.cameraId = cameraId; }
        
        public String getCameraName() { return cameraName; }
        public void setCameraName(String cameraName) { this.cameraName = cameraName; }
        
        public boolean isOverallSuccess() { return overallSuccess; }
        public void setOverallSuccess(boolean overallSuccess) { this.overallSuccess = overallSuccess; }
        
        public boolean isNetworkReachable() { return networkReachable; }
        public void setNetworkReachable(boolean networkReachable) { this.networkReachable = networkReachable; }
        
        public boolean isPortOpen() { return portOpen; }
        public void setPortOpen(boolean portOpen) { this.portOpen = portOpen; }
        
        public List<String> getMessages() { return messages; }
        public void setMessages(List<String> messages) { this.messages = messages; }
        
        public void addMessage(String message) {
            this.messages.add(message);
        }
        
        @Override
        public String toString() {
            return "CameraTestResult{" +
                    "cameraId=" + cameraId +
                    ", cameraName='" + cameraName + '\'' +
                    ", overallSuccess=" + overallSuccess +
                    ", networkReachable=" + networkReachable +
                    ", portOpen=" + portOpen +
                    ", messages=" + messages +
                    '}';
        }
    }
}
