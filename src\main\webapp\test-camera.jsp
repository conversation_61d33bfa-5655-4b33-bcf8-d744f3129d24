<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.building.service.impl.CameraServiceImpl" %>
<%@ page import="com.building.model.Camera" %>
<%@ page import="java.util.List" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>摄像头数据测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .info { background: #e7f3ff; padding: 10px; border-left: 4px solid #2196F3; margin: 10px 0; }
        .error { background: #ffebee; padding: 10px; border-left: 4px solid #f44336; margin: 10px 0; }
        .success { background: #e8f5e8; padding: 10px; border-left: 4px solid #4caf50; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>摄像头数据测试页面</h1>
    
    <%
        try {
            // 检查用户登录状态
            Object user = session.getAttribute("user");
            if (user == null) {
                out.println("<div class='error'>用户未登录，请先登录系统</div>");
                out.println("<a href='" + request.getContextPath() + "/login.jsp'>点击登录</a>");
            } else {
                out.println("<div class='success'>用户已登录: " + user.toString() + "</div>");
            }
            
            // 测试摄像头服务
            CameraServiceImpl cameraService = new CameraServiceImpl();
            out.println("<div class='info'>摄像头服务初始化成功</div>");
            
            // 获取所有摄像头
            List<Camera> cameras = cameraService.getAllCameras();
            out.println("<div class='info'>摄像头总数: " + cameras.size() + "</div>");
            
            if (cameras.isEmpty()) {
                out.println("<div class='error'>数据库中没有摄像头数据！</div>");
                out.println("<p>请检查数据库连接和数据表是否正确创建。</p>");
            } else {
                out.println("<div class='success'>找到 " + cameras.size() + " 个摄像头</div>");
                
                // 显示摄像头列表
                out.println("<h2>摄像头列表</h2>");
                out.println("<table>");
                out.println("<tr><th>ID</th><th>名称</th><th>IP地址</th><th>端口</th><th>位置</th><th>状态</th><th>RTSP URL</th></tr>");
                
                for (Camera camera : cameras) {
                    out.println("<tr>");
                    out.println("<td>" + camera.getId() + "</td>");
                    out.println("<td>" + (camera.getName() != null ? camera.getName() : "未设置") + "</td>");
                    out.println("<td>" + (camera.getIpAddress() != null ? camera.getIpAddress() : "未设置") + "</td>");
                    out.println("<td>" + camera.getPort() + "</td>");
                    out.println("<td>" + (camera.getLocation() != null ? camera.getLocation() : "未设置") + "</td>");
                    out.println("<td>" + (camera.getStatus() == 1 ? "在线" : "离线") + "</td>");
                    out.println("<td>" + (camera.getRtspUrl() != null ? camera.getRtspUrl() : "未设置") + "</td>");
                    out.println("</tr>");
                }
                out.println("</table>");
                
                // 测试获取特定摄像头
                if (cameras.size() > 0) {
                    Camera firstCamera = cameras.get(0);
                    int testId = firstCamera.getId();
                    
                    out.println("<h2>测试获取摄像头详情</h2>");
                    out.println("<div class='info'>测试获取ID为 " + testId + " 的摄像头</div>");
                    
                    Camera testCamera = cameraService.getCameraById(testId);
                    if (testCamera != null) {
                        out.println("<div class='success'>成功获取摄像头: " + testCamera.getName() + "</div>");
                        out.println("<p><a href='" + request.getContextPath() + "/camera/detail?id=" + testId + "'>点击查看详情页面</a></p>");
                    } else {
                        out.println("<div class='error'>无法获取ID为 " + testId + " 的摄像头</div>");
                    }
                }
            }
            
        } catch (Exception e) {
            out.println("<div class='error'>发生错误: " + e.getMessage() + "</div>");
            out.println("<pre>");
            e.printStackTrace(new java.io.PrintWriter(out));
            out.println("</pre>");
        }
    %>
    
    <h2>测试链接</h2>
    <ul>
        <li><a href="<%= request.getContextPath() %>/camera/list">摄像头列表页面</a></li>
        <li><a href="<%= request.getContextPath() %>/camera/detail?id=1">摄像头详情页面 (ID=1)</a></li>
        <li><a href="<%= request.getContextPath() %>/camera/stream?id=1">视频流页面 (ID=1)</a></li>
        <li><a href="<%= request.getContextPath() %>/login.jsp">登录页面</a></li>
    </ul>
    
    <h2>系统信息</h2>
    <div class='info'>
        <p>Context Path: <%= request.getContextPath() %></p>
        <p>Server Info: <%= application.getServerInfo() %></p>
        <p>当前时间: <%= new java.util.Date() %></p>
    </div>
</body>
</html>
