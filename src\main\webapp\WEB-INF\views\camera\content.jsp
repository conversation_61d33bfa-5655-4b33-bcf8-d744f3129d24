<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div class="container-fluid py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-primary fw-bold">
                        <i class="bi bi-camera-video me-2"></i>摄像头管理
                    </h2>
                    <p class="text-muted">管理和监控所有摄像头设备，实时查看视频流和控制摄像头</p>
                </div>
                <div class="d-flex">
                    <div class="input-group me-3">
                        <span class="input-group-text bg-white border-end-0">
                            <i class="bi bi-search text-muted"></i>
                        </span>
                        <input type="text" class="form-control border-start-0 ps-0" placeholder="搜索摄像头..." id="cameraSearchInput">
                    </div>
                    <button class="btn btn-primary rounded-pill px-4 d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#addCameraModal">
                        <i class="bi bi-plus-lg me-2"></i> 添加摄像头
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 摄像头统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="card stats-card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="stats-icon-container">
                        <i class="bi bi-camera-video stats-icon"></i>
                    </div>
                    <h3>${cameraStats.total}</h3>
                    <p>摄像头总数</p>
                    <div class="stats-trend">
                        <i class="bi bi-arrow-up-right"></i> 较上月增长 5%
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card stats-card bg-success text-white h-100">
                <div class="card-body">
                    <div class="stats-icon-container">
                        <i class="bi bi-wifi stats-icon"></i>
                    </div>
                    <h3>${cameraStats.online}</h3>
                    <p>在线摄像头</p>
                    <div class="stats-trend">
                        <i class="bi bi-arrow-up-right"></i> 在线率 ${cameraStats.total > 0 ? Math.round(cameraStats.online * 100 / cameraStats.total) : 0}%
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card stats-card bg-danger text-white h-100">
                <div class="card-body">
                    <div class="stats-icon-container">
                        <i class="bi bi-wifi-off stats-icon"></i>
                    </div>
                    <h3>${cameraStats.offline}</h3>
                    <p>离线摄像头</p>
                    <div class="stats-trend">
                        <i class="bi bi-arrow-down-right"></i> 需要检查 ${cameraStats.offline} 台设备
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选工具栏 -->
    <div class="filter-toolbar d-flex flex-wrap align-items-center mb-4">
        <div class="me-4 mb-2">
            <span class="filter-label">状态:</span>
            <button class="filter-btn active" data-filter="all">全部</button>
            <button class="filter-btn" data-filter="online">在线</button>
            <button class="filter-btn" data-filter="offline">离线</button>
        </div>
        <div class="me-4 mb-2">
            <span class="filter-label">位置:</span>
            <button class="filter-btn active" data-location="all">全部</button>
            <c:set var="locations" value="" />
            <c:forEach items="${cameras}" var="camera">
                <c:if test="${not empty camera.location && !locations.contains(camera.location)}">
                    <c:set var="locations" value="${locations}${camera.location}," />
                    <button class="filter-btn" data-location="${camera.location}">${camera.location}</button>
                </c:if>
            </c:forEach>
        </div>
    </div>

    <!-- 摄像头列表 -->
    <div class="row" id="cameraList">
        <c:forEach items="${cameras}" var="camera">
            <div class="col-md-6 col-lg-4 mb-4 camera-item"
                 data-status="${camera.status == 1 ? 'online' : 'offline'}"
                 data-location="${camera.location}">
                <div class="card camera-card h-100">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0 camera-name">
                                <span class="camera-status ${camera.status == 1 ? 'camera-online' : 'camera-offline'}"></span>
                                ${camera.name}
                            </h5>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary rounded-circle" type="button" id="dropdownMenuButton${camera.id}" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-three-dots-vertical"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end shadow-sm" aria-labelledby="dropdownMenuButton${camera.id}">
                                    <li><a class="dropdown-item" href="javascript:void(0)" onclick="viewCameraDetail(${camera.id})"><i class="bi bi-info-circle me-2"></i>查看详情</a></li>
                                    <li><a class="dropdown-item" href="javascript:void(0)" onclick="viewStream(${camera.id})"><i class="bi bi-play-circle me-2"></i>查看视频流</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <c:if test="${camera.status == 0}">
                                        <li><a class="dropdown-item text-success" href="javascript:void(0)" onclick="connectCamera(${camera.id})"><i class="bi bi-wifi me-2"></i>连接摄像头</a></li>
                                        <li><a class="dropdown-item text-info" href="javascript:void(0)" onclick="testCameraConnection(${camera.id})"><i class="bi bi-activity me-2"></i>测试连接</a></li>
                                    </c:if>
                                    <c:if test="${camera.status == 1}">
                                        <li><a class="dropdown-item text-danger" href="javascript:void(0)" onclick="disconnectCamera(${camera.id})"><i class="bi bi-wifi-off me-2"></i>断开摄像头</a></li>
                                        <li><a class="dropdown-item text-info" href="javascript:void(0)" onclick="testCameraConnection(${camera.id})"><i class="bi bi-activity me-2"></i>测试连接</a></li>
                                    </c:if>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="camera-thumbnail mb-3">
                            <c:choose>
                                <c:when test="${camera.status == 1}">
                                    <img src="${pageContext.request.contextPath}/static/images/camera-preview.jpg" alt="摄像头预览">
                                    <div class="position-absolute top-0 end-0 m-2">
                                        <span class="badge bg-success rounded-pill px-2 py-1">
                                            <i class="bi bi-broadcast me-1"></i> 在线
                                        </span>
                                    </div>
                                </c:when>
                                <c:otherwise>
                                    <i class="bi bi-camera-video-off camera-icon"></i>
                                    <div class="position-absolute top-0 end-0 m-2">
                                        <span class="badge bg-danger rounded-pill px-2 py-1">
                                            <i class="bi bi-wifi-off me-1"></i> 离线
                                        </span>
                                    </div>
                                </c:otherwise>
                            </c:choose>
                        </div>
                        <div class="camera-info-item camera-location">
                            <i class="bi bi-geo-alt"></i>
                            <span>${not empty camera.location ? camera.location : '未设置位置'}</span>
                        </div>
                        <div class="camera-info-item">
                            <i class="bi bi-building"></i>
                            <span>
                                <c:choose>
                                    <c:when test="${camera.room != null}">
                                        ${camera.room.roomNumber} (${camera.room.floorNumber}楼)
                                    </c:when>
                                    <c:otherwise>
                                        <span class="text-muted">未分配房间</span>
                                    </c:otherwise>
                                </c:choose>
                            </span>
                        </div>
                        <div class="camera-info-item">
                            <i class="bi bi-tag"></i>
                            <span>${not empty camera.brand ? camera.brand : '未知品牌'} ${not empty camera.model ? camera.model : '未知型号'}</span>
                        </div>
                        <div class="camera-info-item">
                            <i class="bi bi-ethernet"></i>
                            <span>${camera.ipAddress}:${camera.port}</span>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <button class="btn btn-camera-action btn-outline-primary" onclick="viewCameraDetail(${camera.id})">
                                <i class="bi bi-info-circle"></i> 详情
                            </button>
                            <button class="btn btn-camera-action ${camera.status == 1 ? 'btn-success' : 'btn-outline-secondary'}" onclick="viewStream(${camera.id})">
                                <i class="bi bi-play-circle"></i> 查看视频
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </c:forEach>

        <c:if test="${empty cameras}">
            <div class="col-12">
                <div class="alert alert-info text-center py-5 rounded-4 shadow-sm">
                    <i class="bi bi-camera-video-off fs-1 mb-3 d-block"></i>
                    <h4>暂无摄像头</h4>
                    <p class="mb-3">点击下方按钮添加新的摄像头设备</p>
                    <button class="btn btn-primary rounded-pill px-4" data-bs-toggle="modal" data-bs-target="#addCameraModal">
                        <i class="bi bi-plus-lg me-2"></i> 添加摄像头
                    </button>
                </div>
            </div>
        </c:if>
    </div>
</div>

<!-- Toast 提示容器 -->
<div class="toast-container"></div>

<!-- 添加摄像头模态框 -->
<div class="modal fade" id="addCameraModal" tabindex="-1" aria-labelledby="addCameraModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title fw-bold" id="addCameraModalLabel">
                    <i class="bi bi-plus-circle me-2 text-primary"></i>添加摄像头
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addCameraForm" class="needs-validation" novalidate>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="name" name="name" placeholder="摄像头名称" required>
                                <label for="name">摄像头名称 <span class="text-danger">*</span></label>
                                <div class="invalid-feedback">请输入摄像头名称</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="location" name="location" placeholder="位置">
                                <label for="location">位置</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="ipAddress" name="ipAddress" placeholder="IP地址" required>
                                <label for="ipAddress">IP地址 <span class="text-danger">*</span></label>
                                <div class="invalid-feedback">请输入有效的IP地址</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="number" class="form-control" id="port" name="port" value="554" placeholder="端口号">
                                <label for="port">端口号</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="username" name="username" value="admin" placeholder="用户名">
                                <label for="username">用户名</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="password" class="form-control" id="password" name="password" value="admin" placeholder="密码">
                                <label for="password">密码</label>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="rtspUrl" name="rtspUrl" placeholder="RTSP URL">
                                <label for="rtspUrl">RTSP URL (可选，留空将自动生成)</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="brand" name="brand" placeholder="品牌">
                                <label for="brand">品牌</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="model" name="model" placeholder="型号">
                                <label for="model">型号</label>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-floating">
                                <select class="form-select" id="roomId" name="roomId" aria-label="所属房间">
                                    <option value="">-- 选择房间 --</option>
                                    <c:forEach items="${rooms}" var="room">
                                        <option value="${room.id}">${room.roomNumber} (${room.floorNumber}楼)</option>
                                    </c:forEach>
                                </select>
                                <label for="roomId">所属房间</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary rounded-pill px-4" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg me-2"></i>取消
                </button>
                <button type="submit" form="addCameraForm" class="btn btn-primary rounded-pill px-4">
                    <i class="bi bi-check-lg me-2"></i>添加
                </button>
            </div>
        </div>
    </div>
</div>
