package com.building.servlet;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.building.model.Camera;
import com.building.service.CameraService;
import com.building.service.impl.CameraServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 摄像头控制Servlet
 * 用于处理摄像头控制的请求
 */
@WebServlet("/camera/control")
public class CameraControlServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    private CameraService cameraService;
    private ObjectMapper objectMapper;

    @Override
    public void init() throws ServletException {
        cameraService = new CameraServiceImpl();
        objectMapper = new ObjectMapper();
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // 设置响应类型
        response.setContentType("application/json;charset=UTF-8");

        // 创建结果Map
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取请求参数
            String cameraIdStr = request.getParameter("cameraId");
            String action = request.getParameter("action");

            // 参数验证
            if (cameraIdStr == null || cameraIdStr.trim().isEmpty() ||
                action == null || action.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "摄像头ID和操作不能为空");
                objectMapper.writeValue(response.getWriter(), result);
                return;
            }

            int cameraId = Integer.parseInt(cameraIdStr);

            // 获取摄像头信息
            Camera camera = cameraService.getCameraById(cameraId);
            if (camera == null) {
                result.put("success", false);
                result.put("message", "摄像头不存在");
                objectMapper.writeValue(response.getWriter(), result);
                return;
            }

            // 根据不同的操作执行不同的控制逻辑
            boolean success;
            String message;

            switch (action) {
                case "connect":
                    // 连接摄像头 - 先测试连接再更新状态
                    System.out.println("尝试连接摄像头 ID: " + cameraId);

                    // 测试摄像头连接
                    if (cameraService instanceof com.building.service.impl.CameraServiceImpl) {
                        com.building.service.impl.CameraServiceImpl serviceImpl =
                            (com.building.service.impl.CameraServiceImpl) cameraService;
                        boolean connectionTest = serviceImpl.testCameraConnection(camera);

                        if (connectionTest) {
                            success = cameraService.updateCameraStatus(cameraId, 1);
                            message = success ? "摄像头连接成功" : "摄像头状态更新失败";
                        } else {
                            success = false;
                            message = "摄像头连接测试失败，请检查网络连接和摄像头配置";
                        }
                    } else {
                        // 如果无法进行连接测试，直接更新状态
                        success = cameraService.updateCameraStatus(cameraId, 1);
                        message = success ? "摄像头连接成功" : "摄像头连接失败";
                    }
                    break;

                case "disconnect":
                    // 断开摄像头
                    System.out.println("断开摄像头 ID: " + cameraId);
                    success = cameraService.updateCameraStatus(cameraId, 0);
                    message = success ? "摄像头断开成功" : "摄像头断开失败";
                    break;

                case "test_connection":
                    // 测试摄像头连接（不改变状态）
                    System.out.println("测试摄像头连接 ID: " + cameraId);
                    if (cameraService instanceof com.building.service.impl.CameraServiceImpl) {
                        com.building.service.impl.CameraServiceImpl serviceImpl =
                            (com.building.service.impl.CameraServiceImpl) cameraService;
                        success = serviceImpl.testCameraConnection(camera);
                        message = success ? "摄像头连接测试成功" : "摄像头连接测试失败";
                    } else {
                        success = false;
                        message = "连接测试功能不可用";
                    }
                    break;

                case "pan_left":
                case "pan_right":
                case "tilt_up":
                case "tilt_down":
                case "zoom_in":
                case "zoom_out":
                case "home":
                    // 这些控制功能需要通过摄像头API实现
                    // 目前仅返回模拟成功
                    System.out.println("发送摄像头控制命令: " + action + " 到摄像头 ID: " + cameraId);
                    success = true;
                    message = "摄像头控制命令已发送: " + getActionDescription(action);
                    break;

                default:
                    success = false;
                    message = "不支持的操作: " + action;
                    System.err.println("收到不支持的摄像头控制操作: " + action);
                    break;
            }

            result.put("success", success);
            result.put("message", message);

        } catch (NumberFormatException e) {
            result.put("success", false);
            result.put("message", "无效的摄像头ID");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "处理请求时发生错误: " + e.getMessage());
            e.printStackTrace();
        }

        // 返回JSON结果
        objectMapper.writeValue(response.getWriter(), result);
    }

    /**
     * 获取操作描述
     * @param action 操作类型
     * @return 操作描述
     */
    private String getActionDescription(String action) {
        switch (action) {
            case "pan_left":
                return "向左转动";
            case "pan_right":
                return "向右转动";
            case "tilt_up":
                return "向上转动";
            case "tilt_down":
                return "向下转动";
            case "zoom_in":
                return "放大";
            case "zoom_out":
                return "缩小";
            case "home":
                return "回到初始位置";
            default:
                return action;
        }
    }
}
