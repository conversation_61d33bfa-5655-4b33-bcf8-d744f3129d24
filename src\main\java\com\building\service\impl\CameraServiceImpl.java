package com.building.service.impl;

import java.util.List;
import java.util.Map;

import com.building.dao.CameraDao;
import com.building.dao.PersonRecordDao;
import com.building.model.Camera;
import com.building.model.PersonRecord;
import com.building.service.CameraService;

/**
 * 摄像头服务实现类
 */
public class CameraServiceImpl implements CameraService {

    private CameraDao cameraDao;
    private PersonRecordDao personRecordDao;

    /**
     * 构造函数
     * 初始化DAO对象
     */
    public CameraServiceImpl() {
        cameraDao = new CameraDao();
        personRecordDao = new PersonRecordDao();
    }

    @Override
    public List<Camera> getAllCameras() {
        return cameraDao.getAllCameras();
    }

    @Override
    public Camera getCameraById(int id) {
        if (id <= 0) {
            return null;
        }
        return cameraDao.getCameraById(id);
    }

    @Override
    public List<Camera> getCamerasByRoomId(int roomId) {
        if (roomId <= 0) {
            return null;
        }
        return cameraDao.getCamerasByRoomId(roomId);
    }

    @Override
    public boolean addCamera(Camera camera) {
        // 详细的参数验证
        if (camera == null) {
            System.err.println("添加摄像头失败：摄像头对象为空");
            return false;
        }

        if (camera.getName() == null || camera.getName().trim().isEmpty()) {
            System.err.println("添加摄像头失败：摄像头名称不能为空");
            return false;
        }

        if (camera.getIpAddress() == null || camera.getIpAddress().trim().isEmpty()) {
            System.err.println("添加摄像头失败：IP地址不能为空");
            return false;
        }

        // 使用工具类进行全面的配置验证
        if (!validateCameraConfiguration(camera)) {
            System.err.println("添加摄像头失败：配置验证不通过");
            return false;
        }

        // 如果未设置RTSP URL，则自动生成
        if (camera.getRtspUrl() == null || camera.getRtspUrl().trim().isEmpty()) {
            String rtspUrl = generateRtspUrl(camera);
            camera.setRtspUrl(rtspUrl);
            System.out.println("自动生成RTSP URL：" + rtspUrl);
        }

        return cameraDao.addCamera(camera);
    }

    @Override
    public boolean updateCamera(Camera camera) {
        if (camera == null || camera.getId() <= 0 || camera.getName() == null ||
            camera.getName().trim().isEmpty() || camera.getIpAddress() == null ||
            camera.getIpAddress().trim().isEmpty()) {
            return false;
        }

        // 如果未设置RTSP URL，则自动生成
        if (camera.getRtspUrl() == null || camera.getRtspUrl().trim().isEmpty()) {
            String rtspUrl = generateRtspUrl(camera);
            camera.setRtspUrl(rtspUrl);
        }

        return cameraDao.updateCamera(camera);
    }

    @Override
    public boolean deleteCamera(int id) {
        if (id <= 0) {
            return false;
        }
        return cameraDao.deleteCamera(id);
    }

    @Override
    public boolean updateCameraStatus(int id, int status) {
        if (id <= 0 || (status != 0 && status != 1)) {
            return false;
        }
        return cameraDao.updateCameraStatus(id, status);
    }

    @Override
    public Map<String, Integer> getCameraStats() {
        return cameraDao.getCameraStats();
    }

    @Override
    public boolean addPersonRecord(PersonRecord record) {
        if (record == null || record.getCameraId() <= 0 || record.getRoomId() <= 0) {
            return false;
        }
        return personRecordDao.addRecord(record);
    }

    @Override
    public PersonRecord getLatestPersonRecord(int roomId) {
        if (roomId <= 0) {
            return null;
        }
        return personRecordDao.getLatestRecordByRoomId(roomId);
    }

    @Override
    public List<PersonRecord> getPersonRecordHistory(int roomId, int limit) {
        if (roomId <= 0 || limit <= 0) {
            return null;
        }
        return personRecordDao.getRecordHistoryByRoomId(roomId, limit);
    }

    @Override
    public List<PersonRecord> getPersonRecordsByTimeRange(int roomId, String startTime, String endTime) {
        if (roomId <= 0 || startTime == null || endTime == null) {
            return null;
        }
        return personRecordDao.getRecordsByTimeRange(roomId, startTime, endTime);
    }

    /**
     * 根据摄像头信息生成RTSP URL
     * @param camera 摄像头对象
     * @return RTSP URL
     */
    private String generateRtspUrl(Camera camera) {
        // 根据不同品牌生成不同的RTSP URL
        String brand = camera.getBrand() != null ? camera.getBrand().toLowerCase() : "";
        String username = camera.getUsername() != null ? camera.getUsername() : "admin";
        String password = camera.getPassword() != null ? camera.getPassword() : "admin";
        String ip = camera.getIpAddress();
        int port = camera.getPort() > 0 ? camera.getPort() : 554;

        if (brand.contains("hikvision") || brand.contains("海康")) {
            return "rtsp://" + username + ":" + password + "@" + ip + ":" + port + "/h264/ch1/main/av_stream";
        } else if (brand.contains("dahua") || brand.contains("大华")) {
            return "rtsp://" + username + ":" + password + "@" + ip + ":" + port + "/cam/realmonitor?channel=1&subtype=0";
        } else {
            // 默认RTSP URL格式
            return "rtsp://" + username + ":" + password + "@" + ip + ":" + port + "/live/ch1";
        }
    }

    /**
     * 验证IP地址格式是否正确
     * @param ipAddress IP地址
     * @return 是否有效
     */
    private boolean isValidIpAddress(String ipAddress) {
        if (ipAddress == null || ipAddress.trim().isEmpty()) {
            return false;
        }

        String[] parts = ipAddress.trim().split("\\.");
        if (parts.length != 4) {
            return false;
        }

        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 测试摄像头连接
     * @param camera 摄像头对象
     * @return 连接测试结果
     */
    public boolean testCameraConnection(Camera camera) {
        if (camera == null) {
            System.err.println("测试摄像头连接失败：摄像头对象为空");
            return false;
        }

        System.out.println("开始测试摄像头连接：" + camera.getName() + " (" + camera.getIpAddress() + ")");

        // 使用CameraTestUtil进行全面测试
        com.building.util.CameraTestUtil.CameraTestResult result =
            com.building.util.CameraTestUtil.testNetworkConnectivity(camera);

        // 输出测试结果
        System.out.println("摄像头连接测试结果：");
        for (String message : result.getMessages()) {
            System.out.println("  " + message);
        }

        return result.isOverallSuccess();
    }

    /**
     * 验证摄像头配置
     * @param camera 摄像头对象
     * @return 验证结果
     */
    public boolean validateCameraConfiguration(Camera camera) {
        if (camera == null) {
            System.err.println("验证摄像头配置失败：摄像头对象为空");
            return false;
        }

        com.building.util.CameraTestUtil.CameraTestResult result =
            com.building.util.CameraTestUtil.validateCameraConfig(camera);

        // 输出验证结果
        System.out.println("摄像头配置验证结果：");
        for (String message : result.getMessages()) {
            System.out.println("  " + message);
        }

        return result.isOverallSuccess();
    }

    /**
     * 生成摄像头诊断报告
     * @param camera 摄像头对象
     * @return 诊断报告
     */
    public String generateDiagnosticReport(Camera camera) {
        if (camera == null) {
            return "错误：摄像头对象为空，无法生成诊断报告";
        }

        return com.building.util.CameraTestUtil.generateDiagnosticReport(camera);
    }
}
