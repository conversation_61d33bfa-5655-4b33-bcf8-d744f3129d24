package com.building.servlet;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.building.model.Camera;
import com.building.model.PersonRecord;
import com.building.service.CameraService;
import com.building.service.impl.CameraServiceImpl;

/**
 * 摄像头详情测试Servlet
 * 用于测试摄像头详情功能，提供详细的错误信息
 */
@WebServlet("/camera/detail-test")
public class CameraDetailTestServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    private CameraService cameraService;

    @Override
    public void init() throws ServletException {
        try {
            cameraService = new CameraServiceImpl();
            System.out.println("CameraDetailTestServlet 初始化成功");
        } catch (Exception e) {
            System.err.println("CameraDetailTestServlet 初始化失败: " + e.getMessage());
            e.printStackTrace();
            throw new ServletException("初始化失败", e);
        }
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        try {
            out.println("<!DOCTYPE html>");
            out.println("<html><head><title>摄像头详情测试</title>");
            out.println("<style>");
            out.println("body { font-family: Arial, sans-serif; margin: 20px; }");
            out.println(".info { background: #e7f3ff; padding: 10px; border-left: 4px solid #2196F3; margin: 10px 0; }");
            out.println(".error { background: #ffebee; padding: 10px; border-left: 4px solid #f44336; margin: 10px 0; }");
            out.println(".success { background: #e8f5e8; padding: 10px; border-left: 4px solid #4caf50; margin: 10px 0; }");
            out.println("</style></head><body>");
            
            out.println("<h1>摄像头详情功能测试</h1>");
            
            // 步骤1: 检查用户登录状态
            out.println("<h2>步骤1: 检查用户登录状态</h2>");
            HttpSession session = request.getSession();
            Object user = session.getAttribute("user");
            
            if (user == null) {
                out.println("<div class='error'>❌ 用户未登录</div>");
                out.println("<p>请先<a href='" + request.getContextPath() + "/login.jsp'>登录系统</a></p>");
                
                // 为了测试，我们创建一个临时用户会话
                session.setAttribute("user", "testUser");
                out.println("<div class='info'>ℹ️ 已创建临时用户会话用于测试</div>");
            } else {
                out.println("<div class='success'>✅ 用户已登录: " + user.toString() + "</div>");
            }
            
            // 步骤2: 获取摄像头ID参数
            out.println("<h2>步骤2: 获取摄像头ID参数</h2>");
            String idParam = request.getParameter("id");
            
            if (idParam == null || idParam.trim().isEmpty()) {
                out.println("<div class='error'>❌ 摄像头ID参数为空</div>");
                out.println("<p>请在URL中添加id参数，例如: ?id=1</p>");
                
                // 显示可用的摄像头ID
                try {
                    List<Camera> cameras = cameraService.getAllCameras();
                    if (!cameras.isEmpty()) {
                        out.println("<p>可用的摄像头ID:</p><ul>");
                        for (Camera camera : cameras) {
                            out.println("<li><a href='?id=" + camera.getId() + "'>ID: " + camera.getId() + " - " + camera.getName() + "</a></li>");
                        }
                        out.println("</ul>");
                    }
                } catch (Exception e) {
                    out.println("<div class='error'>获取摄像头列表失败: " + e.getMessage() + "</div>");
                }
                
                out.println("</body></html>");
                return;
            }
            
            out.println("<div class='success'>✅ 摄像头ID参数: " + idParam + "</div>");
            
            // 步骤3: 解析摄像头ID
            out.println("<h2>步骤3: 解析摄像头ID</h2>");
            int cameraId;
            try {
                cameraId = Integer.parseInt(idParam);
                out.println("<div class='success'>✅ 摄像头ID解析成功: " + cameraId + "</div>");
            } catch (NumberFormatException e) {
                out.println("<div class='error'>❌ 摄像头ID格式错误: " + idParam + "</div>");
                out.println("</body></html>");
                return;
            }
            
            // 步骤4: 获取摄像头信息
            out.println("<h2>步骤4: 获取摄像头信息</h2>");
            Camera camera;
            try {
                camera = cameraService.getCameraById(cameraId);
                if (camera == null) {
                    out.println("<div class='error'>❌ 摄像头不存在，ID: " + cameraId + "</div>");
                    out.println("</body></html>");
                    return;
                }
                out.println("<div class='success'>✅ 摄像头信息获取成功</div>");
                out.println("<ul>");
                out.println("<li>ID: " + camera.getId() + "</li>");
                out.println("<li>名称: " + (camera.getName() != null ? camera.getName() : "未设置") + "</li>");
                out.println("<li>IP地址: " + (camera.getIpAddress() != null ? camera.getIpAddress() : "未设置") + "</li>");
                out.println("<li>端口: " + camera.getPort() + "</li>");
                out.println("<li>位置: " + (camera.getLocation() != null ? camera.getLocation() : "未设置") + "</li>");
                out.println("<li>状态: " + (camera.getStatus() == 1 ? "在线" : "离线") + "</li>");
                out.println("<li>RTSP URL: " + (camera.getRtspUrl() != null ? camera.getRtspUrl() : "未设置") + "</li>");
                out.println("</ul>");
            } catch (Exception e) {
                out.println("<div class='error'>❌ 获取摄像头信息失败: " + e.getMessage() + "</div>");
                e.printStackTrace();
                out.println("</body></html>");
                return;
            }
            
            // 步骤5: 获取人员记录
            out.println("<h2>步骤5: 获取人员记录</h2>");
            try {
                if (camera.getRoomId() > 0) {
                    out.println("<div class='info'>ℹ️ 摄像头分配到房间ID: " + camera.getRoomId() + "</div>");
                    
                    List<PersonRecord> records = cameraService.getPersonRecordHistory(camera.getRoomId(), 10);
                    if (records != null && !records.isEmpty()) {
                        out.println("<div class='success'>✅ 人员记录获取成功，数量: " + records.size() + "</div>");
                    } else {
                        out.println("<div class='info'>ℹ️ 暂无人员记录</div>");
                    }
                    
                    PersonRecord latestRecord = cameraService.getLatestPersonRecord(camera.getRoomId());
                    if (latestRecord != null) {
                        out.println("<div class='success'>✅ 最新人员记录获取成功</div>");
                    } else {
                        out.println("<div class='info'>ℹ️ 暂无最新人员记录</div>");
                    }
                } else {
                    out.println("<div class='info'>ℹ️ 摄像头未分配房间</div>");
                }
            } catch (Exception e) {
                out.println("<div class='error'>❌ 获取人员记录失败: " + e.getMessage() + "</div>");
                e.printStackTrace();
            }
            
            // 步骤6: 测试JSP转发
            out.println("<h2>步骤6: 测试结果</h2>");
            out.println("<div class='success'>✅ 所有测试步骤完成，摄像头详情功能正常</div>");
            out.println("<p><a href='" + request.getContextPath() + "/camera/detail?id=" + cameraId + "'>点击访问正式的摄像头详情页面</a></p>");
            out.println("<p><a href='" + request.getContextPath() + "/camera/list'>返回摄像头列表</a></p>");
            
        } catch (Exception e) {
            out.println("<div class='error'>❌ 测试过程中发生未知错误: " + e.getMessage() + "</div>");
            out.println("<pre>");
            e.printStackTrace(out);
            out.println("</pre>");
        } finally {
            out.println("</body></html>");
        }
    }
}
