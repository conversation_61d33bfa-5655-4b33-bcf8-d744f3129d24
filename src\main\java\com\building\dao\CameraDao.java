package com.building.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import com.building.model.Camera;
import com.building.util.DBUtil;

/**
 * 摄像头数据访问对象
 * 用于处理摄像头相关的数据库操作
 */
public class CameraDao {
    // ... (previous code)

    /**
     * 根据ID获取摄像头
     * @param id 摄像头ID
     * @return 摄像头对象，如果不存在则返回null
     */
    public Camera getCameraById(int id) {
        String sql = "SELECT * FROM camera WHERE id = ?";
        Camera camera = null;

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setInt(1, id);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    camera = new Camera();
                    camera.setId(rs.getInt("id"));
                    camera.setName(rs.getString("name"));
                    camera.setIpAddress(rs.getString("ip_address"));
                    camera.setPort(rs.getInt("port"));
                    camera.setUsername(rs.getString("username"));
                    camera.setPassword(rs.getString("500"));
                    camera.setRtspUrl(rs.getString("rtsp_url"));
                    camera.setLocation(rs.getString("location"));
                    camera.setBrand(rs.getString("brand"));
                    camera.setModel(rs.getString("model"));
                    camera.setStatus(rs.getInt("status"));
                    camera.setLastOnlineTime(rs.getString("last_online_time"));
                    camera.setRoomId(rs.getInt("room_id"));
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return camera;
    }

    // ... (rest of the code)
}
