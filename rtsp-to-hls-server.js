/**
 * RTSP到HLS转换服务器
 * 使用Node Media Server将RTSP流转换为HLS流供浏览器播放
 * 
 * 安装依赖：
 * npm install node-media-server
 * 
 * 运行：
 * node rtsp-to-hls-server.js
 */

const NodeMediaServer = require('node-media-server');

const config = {
  rtmp: {
    port: 1935,
    chunk_size: 60000,
    gop_cache: true,
    ping: 30,
    ping_timeout: 60
  },
  http: {
    port: 8888,
    allow_origin: '*',
    mediaroot: './media'
  },
  relay: {
    ffmpeg: 'C:/ffmpeg/bin/ffmpeg.exe', // 请根据您的FFmpeg安装路径修改
    tasks: [
      {
        app: 'live',
        mode: 'pull',
        edge: 'rtsp://admin:admin@192.168.254.2:554/h264/ch1/main/av_stream', // 您的RTSP地址
        name: 'camera1',
        rtmp_url: 'rtmp://127.0.0.1:1935/live/camera1'
      }
    ]
  }
};

const nms = new NodeMediaServer(config);

nms.on('preConnect', (id, args) => {
  console.log('[NodeEvent on preConnect]', `id=${id} args=${JSON.stringify(args)}`);
});

nms.on('postConnect', (id, args) => {
  console.log('[NodeEvent on postConnect]', `id=${id} args=${JSON.stringify(args)}`);
});

nms.on('doneConnect', (id, args) => {
  console.log('[NodeEvent on doneConnect]', `id=${id} args=${JSON.stringify(args)}`);
});

nms.on('prePublish', (id, StreamPath, args) => {
  console.log('[NodeEvent on prePublish]', `id=${id} StreamPath=${StreamPath} args=${JSON.stringify(args)}`);
});

nms.on('postPublish', (id, StreamPath, args) => {
  console.log('[NodeEvent on postPublish]', `id=${id} StreamPath=${StreamPath} args=${JSON.stringify(args)}`);
});

nms.on('donePublish', (id, StreamPath, args) => {
  console.log('[NodeEvent on donePublish]', `id=${id} StreamPath=${StreamPath} args=${JSON.stringify(args)}`);
});

nms.run();

console.log('Node Media Server 已启动');
console.log('RTMP服务器: rtmp://localhost:1935');
console.log('HTTP服务器: http://localhost:8888');
console.log('HLS播放地址: http://localhost:8888/live/camera1.m3u8');
console.log('');
console.log('请确保：');
console.log('1. 已安装FFmpeg并配置正确路径');
console.log('2. 摄像头RTSP地址可访问');
console.log('3. 防火墙允许1935和8888端口');
