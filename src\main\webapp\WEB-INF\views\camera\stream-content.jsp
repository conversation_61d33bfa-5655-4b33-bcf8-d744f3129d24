<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div class="container-fluid py-4">
    <!-- 返回按钮 -->
    <div class="mb-4">
        <a href="${pageContext.request.contextPath}/camera/detail?id=${camera.id}" class="btn btn-outline-primary">
            <i class="bi bi-arrow-left"></i> 返回摄像头详情
        </a>
    </div>

    <!-- 视频流容器 -->
    <div class="stream-container">
        <!-- 视频流信息 -->
        <div class="stream-info">
            <h5 class="mb-1">
                <span class="camera-status ${camera.status == 1 ? 'camera-online' : 'camera-offline'}"></span>
                ${not empty camera.name ? camera.name : '未命名摄像头'}
            </h5>
            <p class="mb-0 text-white-50">
                <i class="bi bi-geo-alt me-1"></i> ${not empty camera.location ? camera.location : '未设置位置'}
                <c:if test="${camera.room != null}">
                    <span class="mx-2">|</span>
                    <i class="bi bi-building me-1"></i> ${not empty camera.room.roomNumber ? camera.room.roomNumber : '未知房间'}
                </c:if>
            </p>
        </div>

        <!-- 视频流内容 -->
        <c:choose>
            <c:when test="${camera.status == 1}">
                <!-- 真实视频流播放器 -->
                <div id="videoContainer" class="video-container">
                    <!-- HLS.js 播放器 -->
                    <video id="videoPlayer" controls autoplay muted style="width: 100%; height: 100%; object-fit: cover;">
                        <source id="videoSource" src="" type="application/x-mpegURL">
                        您的浏览器不支持视频播放。
                    </video>

                    <!-- 备用：显示RTSP URL供用户使用VLC等播放器 -->
                    <div id="rtspInfo" class="rtsp-info mt-3" style="display: none;">
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle me-2"></i>RTSP流地址：</h6>
                            <code id="rtspUrl">${not empty camera.rtspUrl ? camera.rtspUrl : '未设置RTSP地址'}</code>
                            <br><small class="text-muted">请使用VLC播放器或其他支持RTSP的播放器打开此地址</small>
                            <c:if test="${not empty camera.rtspUrl}">
                                <button class="btn btn-sm btn-outline-primary ms-2" onclick="copyRtspUrl()">
                                    <i class="bi bi-clipboard"></i> 复制
                                </button>
                            </c:if>
                        </div>
                    </div>
                </div>
            </c:when>
            <c:otherwise>
                <div class="stream-overlay">
                    <i class="bi bi-camera-video-off mb-3 fs-1"></i>
                    <h4>摄像头离线</h4>
                    <p>请连接摄像头后查看视频流</p>
                    <button id="connectBtn" class="btn btn-primary mt-3" data-action="connect">
                        <i class="bi bi-wifi me-1"></i> 连接摄像头
                    </button>
                </div>
            </c:otherwise>
        </c:choose>

        <!-- 视频流控制 -->
        <c:if test="${camera.status == 1}">
            <div class="stream-controls">
                <button class="btn control-btn" data-action="pan_left">
                    <i class="bi bi-arrow-left"></i>
                </button>
                <button class="btn control-btn" data-action="tilt_up">
                    <i class="bi bi-arrow-up"></i>
                </button>
                <button class="btn control-btn" data-action="home">
                    <i class="bi bi-house"></i>
                </button>
                <button class="btn control-btn" data-action="tilt_down">
                    <i class="bi bi-arrow-down"></i>
                </button>
                <button class="btn control-btn" data-action="pan_right">
                    <i class="bi bi-arrow-right"></i>
                </button>
                <button class="btn control-btn" data-action="zoom_out">
                    <i class="bi bi-dash-lg"></i>
                </button>
                <button class="btn control-btn" data-action="zoom_in">
                    <i class="bi bi-plus-lg"></i>
                </button>
                <button class="btn btn-primary" id="fullscreenBtn">
                    <i class="bi bi-fullscreen"></i>
                </button>
                <button id="connectBtn" class="btn btn-danger" data-action="disconnect">
                    <i class="bi bi-wifi-off"></i>
                </button>
            </div>
        </c:if>
    </div>
</div>
